#!/usr/bin/env python3
"""
Daily MNQ Data Download Scheduler

A robust wrapper script for automated daily downloads of MNQ futures data.
Designed to be run via cron with intelligent market calendar awareness,
error handling, and data validation.

Author: Nautilus Trader Automation
Created: June 2025
"""

import os
import sys
import logging
import json
import subprocess
import time
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse

# Add the project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Configure logging
def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
    """Setup structured logging for the scheduler."""
    logger = logging.getLogger("MNQScheduler")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


class MarketCalendar:
    """Handle market calendar logic for futures trading."""
    
    def __init__(self):
        # Common market holidays (extend as needed)
        self.holidays_2025 = {
            date(2025, 1, 1): "New Year's Day",
            date(2025, 1, 20): "Martin Luther King Jr. Day",
            date(2025, 2, 17): "Presidents Day",
            date(2025, 5, 26): "Memorial Day",
            date(2025, 6, 19): "Juneteenth",
            date(2025, 7, 4): "Independence Day",
            date(2025, 9, 1): "Labor Day",
            date(2025, 11, 27): "Thanksgiving",
            date(2025, 12, 25): "Christmas Day",
        }
    
    def is_trading_day(self, check_date: date) -> bool:
        """
        Check if a given date is a trading day for futures.
        
        Args:
            check_date: Date to check
            
        Returns:
            True if it's a trading day
        """
        # Check if it's a weekend (Saturday = 5, Sunday = 6)
        if check_date.weekday() >= 5:
            return False
            
        # Check if it's a holiday
        if check_date in self.holidays_2025:
            return False
            
        return True
    
    def get_previous_trading_day(self, from_date: Optional[date] = None) -> date:
        """
        Get the previous trading day from a given date.
        
        Args:
            from_date: Starting date (defaults to today)
            
        Returns:
            Previous trading day
        """
        if from_date is None:
            from_date = date.today()
            
        check_date = from_date - timedelta(days=1)
        
        # Keep going back until we find a trading day
        while not self.is_trading_day(check_date):
            check_date -= timedelta(days=1)
            
        return check_date
    
    def get_holiday_name(self, check_date: date) -> Optional[str]:
        """Get the name of the holiday if the date is a holiday."""
        return self.holidays_2025.get(check_date)


class DataValidator:
    """Validate downloaded data quality and completeness."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def validate_download_result(self, result_output: str, expected_days: int = 1) -> Dict[str, any]:
        """
        Validate the download result based on script output.
        
        Args:
            result_output: Output from download script
            expected_days: Number of days we expected to download
            
        Returns:
            Validation result dictionary
        """
        validation = {
            "success": False,
            "bars_downloaded": 0,
            "bars_saved": 0,
            "issues": []
        }
        
        try:
            # Parse output for success indicators
            if "✓ Download completed successfully" in result_output:
                validation["success"] = True
            
            # Extract bar counts
            lines = result_output.split('\n')
            for line in lines:
                if "Downloaded " in line and " bars" in line:
                    try:
                        # Extract number from "Downloaded X,XXX bars"
                        parts = line.split("Downloaded ")[1].split(" bars")[0]
                        validation["bars_downloaded"] = int(parts.replace(",", ""))
                    except (IndexError, ValueError):
                        pass
                        
                if "Saved " in line and " bars" in line:
                    try:
                        # Extract number from "Saved X,XXX bars"
                        parts = line.split("Saved ")[1].split(" bars")[0]
                        validation["bars_saved"] = int(parts.replace(",", ""))
                    except (IndexError, ValueError):
                        pass
            
            # Validate bar counts
            if validation["bars_downloaded"] == 0:
                validation["issues"].append("No bars downloaded")
                validation["success"] = False
            
            # For MNQ, expect roughly 1380 bars per trading day (23 hours * 60 minutes)
            expected_bars_min = expected_days * 1200  # Conservative estimate
            expected_bars_max = expected_days * 1500  # Liberal estimate
            
            if validation["bars_downloaded"] < expected_bars_min:
                validation["issues"].append(
                    f"Bar count too low: {validation['bars_downloaded']} < {expected_bars_min}"
                )
            elif validation["bars_downloaded"] > expected_bars_max:
                validation["issues"].append(
                    f"Bar count unexpectedly high: {validation['bars_downloaded']} > {expected_bars_max}"
                )
            
            # Check for common error patterns
            error_patterns = [
                "Error saving to Nautilus catalog",
                "Download failed",
                "Connection failed",
                "No data downloaded",
                "Aborted"
            ]
            
            for pattern in error_patterns:
                if pattern in result_output:
                    validation["issues"].append(f"Error detected: {pattern}")
                    validation["success"] = False
            
        except Exception as e:
            validation["issues"].append(f"Validation error: {str(e)}")
            validation["success"] = False
        
        return validation


class MNQScheduler:
    """Main scheduler class for automated MNQ data downloads."""
    
    def __init__(self, config_file: Optional[str] = None):
        # Load configuration
        self.config = self.load_config(config_file)
        
        # Setup logging
        self.logger = setup_logging(
            self.config.get("log_level", "INFO"),
            self.config.get("log_file")
        )
        
        # Initialize components
        self.market_calendar = MarketCalendar()
        self.validator = DataValidator(self.logger)
        
        # Set working directory
        self.project_root = PROJECT_ROOT
        self.download_script = self.project_root / "user_scripts_restructured" / "scripts" / "download_data.py"
    
    def load_config(self, config_file: Optional[str] = None) -> Dict:
        """Load configuration from file or use defaults."""
        default_config = {
            "log_level": "INFO",
            "log_file": "/var/log/mnq_scheduler.log",
            "symbol": "MNQ",
            "asset_type": "futures",
            "max_retries": 3,
            "retry_delay": 60,  # seconds
            "force_download": False,
            "use_rth": False,
            "download_timeout": 300,  # seconds
        }
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
                # Note: logger not available yet during initialization
                print(f"Loaded configuration from {config_file}")
            except Exception as e:
                print(f"Warning: Could not load config file {config_file}: {e}")
        
        return default_config
    
    def check_environment(self) -> bool:
        """Check if the environment is properly set up."""
        try:
            # Check if download script exists
            if not self.download_script.exists():
                self.logger.error(f"Download script not found: {self.download_script}")
                return False
            
            # Check if we can import required modules (basic test)
            try:
                import pandas as pd
                import numpy as np
            except ImportError as e:
                self.logger.error(f"Required Python modules not available: {e}")
                return False
            
            # Check if we're in the right directory
            if not (self.project_root / "pyproject.toml").exists():
                self.logger.warning("Not in expected Nautilus Trader directory")
            
            self.logger.info("Environment check passed")
            return True
            
        except Exception as e:
            self.logger.error(f"Environment check failed: {e}")
            return False
    
    def run_download(self, target_date: date, retry_count: int = 0) -> Tuple[bool, str]:
        """
        Run the download for a specific date.
        
        Args:
            target_date: Date to download data for
            retry_count: Current retry attempt (for logging)
            
        Returns:
            Tuple of (success, output)
        """
        try:
            # Build command
            cmd = [
                sys.executable,
                str(self.download_script),
                "--asset-type", self.config["asset_type"],
                "--symbol", self.config["symbol"],
                "--start-date", target_date.strftime("%Y-%m-%d"),
                "--end-date", target_date.strftime("%Y-%m-%d"),
                "--log-level", self.config["log_level"]
            ]
            
            if self.config["force_download"]:
                cmd.append("--force")
            
            if self.config["use_rth"]:
                cmd.append("--use-rth")
            
            # Log the command (for debugging)
            cmd_str = " ".join(cmd)
            if retry_count > 0:
                self.logger.info(f"Download attempt {retry_count + 1}: {cmd_str}")
            else:
                self.logger.info(f"Starting download: {cmd_str}")
            
            # Run the command
            result = subprocess.run(
                cmd,
                cwd=str(self.project_root),
                capture_output=True,
                text=True,
                timeout=self.config["download_timeout"]
            )
            
            # Combine stdout and stderr
            output = result.stdout + "\n" + result.stderr
            
            # Check return code
            if result.returncode == 0:
                self.logger.info(f"Download completed with return code {result.returncode}")
                return True, output
            else:
                self.logger.error(f"Download failed with return code {result.returncode}")
                self.logger.error(f"Error output: {result.stderr}")
                return False, output
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"Download timed out after {self.config['download_timeout']} seconds")
            return False, "Download timed out"
        except Exception as e:
            self.logger.error(f"Download execution error: {e}")
            return False, str(e)
    
    def run_with_retries(self, target_date: date) -> bool:
        """
        Run download with retry logic.
        
        Args:
            target_date: Date to download data for
            
        Returns:
            True if successful
        """
        max_retries = self.config["max_retries"]
        retry_delay = self.config["retry_delay"]
        
        for attempt in range(max_retries):
            success, output = self.run_download(target_date, attempt)
            
            if success:
                # Validate the results
                validation = self.validator.validate_download_result(output)
                
                if validation["success"]:
                    self.logger.info(f"✓ Download successful: {validation['bars_downloaded']} bars downloaded, {validation['bars_saved']} bars saved")
                    return True
                else:
                    self.logger.warning(f"Download completed but validation failed: {validation['issues']}")
                    if attempt < max_retries - 1:
                        self.logger.info(f"Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                        continue
            else:
                self.logger.error(f"Download attempt {attempt + 1} failed")
                if attempt < max_retries - 1:
                    self.logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    self.logger.error("All retry attempts exhausted")
        
        return False
    
    def run_daily_job(self, force_date: Optional[date] = None) -> bool:
        """
        Run the daily download job.
        
        Args:
            force_date: Force download for specific date (for testing)
            
        Returns:
            True if successful
        """
        try:
            start_time = datetime.now()
            self.logger.info("=" * 60)
            self.logger.info("MNQ DAILY DOWNLOAD SCHEDULER STARTED")
            self.logger.info("=" * 60)
            self.logger.info(f"Scheduler started at: {start_time}")
            
            # Determine target date
            if force_date:
                target_date = force_date
                self.logger.info(f"Using forced date: {target_date}")
            else:
                target_date = self.market_calendar.get_previous_trading_day()
                self.logger.info(f"Target date (previous trading day): {target_date}")
            
            # Check if target date is a trading day
            if not self.market_calendar.is_trading_day(target_date):
                holiday_name = self.market_calendar.get_holiday_name(target_date)
                if holiday_name:
                    self.logger.info(f"Skipping download - {target_date} is a holiday: {holiday_name}")
                else:
                    self.logger.info(f"Skipping download - {target_date} is not a trading day")
                return True
            
            # Environment check
            if not self.check_environment():
                self.logger.error("Environment check failed - aborting")
                return False
            
            # Run download with retries
            success = self.run_with_retries(target_date)
            
            # Summary
            end_time = datetime.now()
            duration = end_time - start_time
            
            self.logger.info("=" * 60)
            if success:
                self.logger.info("✓ DAILY DOWNLOAD COMPLETED SUCCESSFULLY")
            else:
                self.logger.error("✗ DAILY DOWNLOAD FAILED")
            
            self.logger.info(f"Total duration: {duration}")
            self.logger.info(f"Target date: {target_date}")
            self.logger.info("=" * 60)
            
            return success
            
        except Exception as e:
            self.logger.error(f"Unexpected error in daily job: {e}")
            return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Daily MNQ Data Download Scheduler")
    parser.add_argument("--config", type=str, help="Configuration file path")
    parser.add_argument("--date", type=str, help="Force download for specific date (YYYY-MM-DD)")
    parser.add_argument("--dry-run", action="store_true", help="Check environment and target date without downloading")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], default="INFO", help="Logging level")
    
    args = parser.parse_args()
    
    # Create scheduler
    scheduler = MNQScheduler(args.config)
    
    # Override log level if specified
    if args.log_level:
        scheduler.logger.setLevel(getattr(logging, args.log_level))
    
    # Parse forced date if provided
    force_date = None
    if args.date:
        try:
            force_date = datetime.strptime(args.date, "%Y-%m-%d").date()
        except ValueError:
            scheduler.logger.error(f"Invalid date format: {args.date}. Use YYYY-MM-DD")
            return 1
    
    # Dry run mode
    if args.dry_run:
        scheduler.logger.info("DRY RUN MODE - No downloads will be performed")
        
        # Environment check
        env_ok = scheduler.check_environment()
        
        # Date logic check
        target_date = force_date or scheduler.market_calendar.get_previous_trading_day()
        is_trading = scheduler.market_calendar.is_trading_day(target_date)
        
        scheduler.logger.info(f"Environment check: {'✓ PASS' if env_ok else '✗ FAIL'}")
        scheduler.logger.info(f"Target date: {target_date}")
        scheduler.logger.info(f"Is trading day: {'✓ YES' if is_trading else '✗ NO'}")
        
        if not is_trading:
            holiday_name = scheduler.market_calendar.get_holiday_name(target_date)
            if holiday_name:
                scheduler.logger.info(f"Holiday: {holiday_name}")
        
        return 0 if env_ok else 1
    
    # Run the daily job
    success = scheduler.run_daily_job(force_date)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
