#!/bin/bash
"""
Cron Setup Helper for MNQ Daily Scheduler

This script helps set up the cron job for automated daily MNQ data downloads.
"""

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCHEDULER_SCRIPT="$SCRIPT_DIR/daily_mnq_scheduler.py"
CONFIG_FILE="$SCRIPT_DIR/scheduler_config.json"
LOG_DIR="/home/<USER>/nautilus_trader_fork/logs"

echo "MNQ Daily Scheduler - Cron Setup Helper"
echo "======================================="
echo
echo "Scheduler script: $SCHEDULER_SCRIPT"
echo "Config file: $CONFIG_FILE"
echo "Log directory: $LOG_DIR"
echo

# Make sure script is executable
chmod +x "$SCHEDULER_SCRIPT"

# Create log directory
mkdir -p "$LOG_DIR"

# Test the scheduler
echo "Testing scheduler (dry run)..."
python3 "$SCHEDULER_SCRIPT" --config "$CONFIG_FILE" --dry-run

if [ $? -eq 0 ]; then
    echo "✓ Dry run successful!"
else
    echo "✗ Dry run failed. Please check the configuration."
    exit 1
fi

echo
echo "Recommended Cron Entries:"
echo "========================="
echo
echo "Option 1: Daily at 2 AM ET (recommended)"
echo "0 2 * * * $SCHEDULER_SCRIPT --config $CONFIG_FILE >> $LOG_DIR/cron_output.log 2>&1"
echo
echo "Option 2: Weekdays only at 7 PM ET (after market close)"
echo "0 19 * * 1-5 $SCHEDULER_SCRIPT --config $CONFIG_FILE >> $LOG_DIR/cron_output.log 2>&1"
echo
echo "Option 3: Manual testing"
echo "$SCHEDULER_SCRIPT --config $CONFIG_FILE --date 2025-06-09"
echo
echo "To add to cron:"
echo "1. Run: crontab -e"
echo "2. Add one of the lines above"
echo "3. Save and exit"
echo
echo "To view cron logs:"
echo "tail -f $LOG_DIR/mnq_scheduler.log"
echo "tail -f $LOG_DIR/cron_output.log"
