"""
Base downloader class providing common functionality.

Defines the interface and common patterns for all data downloaders.
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from pathlib import Path

# Using full import paths from PYTHONPATH

from user_scripts_restructured.core.config import Config
from user_scripts_restructured.core.ib_client import IBClient, IBBarData
from user_scripts_restructured.core.nautilus_data_saver import NautilusDataSaver, SaveResult

logger = logging.getLogger(__name__)


@dataclass
class DownloadResult:
    """Result of a download operation."""
    success: bool
    bars_downloaded: int
    date_range: Tuple[date, date]
    failed_dates: List[date]
    skipped_dates: List[date]
    error_message: str = ""
    save_result: Optional[SaveResult] = None


class BaseDownloader(ABC):
    """
    Abstract base class for data downloaders.

    Provides common functionality for downloading data from Interactive Brokers
    including connection management, retry logic, and data validation.
    """

    def __init__(self, config: Config):
        """
        Initialize the downloader.

        Args:
            config: Configuration object
        """
        self.config = config
        self.ib_client = IBClient(config.ib)
        
        # Initialize Nautilus data saver if enabled
        self.use_nautilus_saver = getattr(config, 'nautilus', {}).get('enabled', True)
        self.catalog_path = getattr(config, 'nautilus', {}).get('catalog_path', 
                                  '/home/<USER>/nautilus_trader_fork/catalog')
        
        if self.use_nautilus_saver:
            try:
                self.nautilus_saver = NautilusDataSaver(catalog_path=self.catalog_path)
                logger.info("Nautilus data saver initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Nautilus data saver: {e}")
                logger.warning("Will use fallback data saving")
                self.use_nautilus_saver = False
                self.nautilus_saver = None
        else:
            self.nautilus_saver = None
            logger.info("Nautilus data saver disabled in configuration")

    @abstractmethod
    def download(self, symbol: str, start_date: date, end_date: date,
                **kwargs) -> DownloadResult:
        """
        Download data for the specified symbol and date range.

        Args:
            symbol: Symbol to download
            start_date: Start date
            end_date: End date
            **kwargs: Additional parameters specific to the downloader

        Returns:
            DownloadResult object
        """
        pass

    @abstractmethod
    def _create_contract(self, symbol: str, **kwargs):
        """
        Create an IB contract for the symbol.

        Args:
            symbol: Symbol to create contract for
            **kwargs: Additional parameters

        Returns:
            IB Contract object
        """
        pass

    def _validate_date_range(self, start_date: date, end_date: date) -> Tuple[date, date]:
        """
        Validate and adjust the date range.

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            Tuple of validated (start_date, end_date)
        """
        today = date.today()
        yesterday = today - timedelta(days=1)

        # If end date is in the future, cap it at yesterday
        if end_date > yesterday:
            logger.warning(f"End date {end_date} is in the future. Capping at yesterday ({yesterday}).")
            end_date = yesterday

        # If start date is after end date, swap them
        if start_date > end_date:
            logger.warning(f"Start date {start_date} is after end date {end_date}. Swapping dates.")
            start_date, end_date = end_date, start_date

        return start_date, end_date

    def _download_single_day(self, contract, target_date: date,
                           use_rth: bool = False, **kwargs) -> List[IBBarData]:
        """
        Download data for a single day.

        Args:
            contract: IB contract
            target_date: Date to download
            use_rth: Use regular trading hours only
            **kwargs: Additional parameters

        Returns:
            List of IB bar data
        """
        # Default implementation - subclasses can override
        end_dt = datetime.combine(target_date, datetime.max.time())
        end_str = end_dt.strftime("%Y%m%d %H:%M:%S")

        return self.ib_client.download_with_retry(
            contract=contract,
            end_date_str=f"{end_str} UTC",
            duration="1 D",
            bar_size="1 min",
            use_rth=use_rth
        )

    def _convert_ib_bars_to_dict(self, ib_bars: List[IBBarData]) -> List[Dict[str, Any]]:
        """
        Convert IB bar data to dictionary format.

        Args:
            ib_bars: List of IB bar data

        Returns:
            List of dictionaries
        """
        dict_bars = []

        for bar in ib_bars:
            try:
                # Convert IB timestamp to nanoseconds
                ts_event = int(float(bar.date) * 1e9)

                dict_bar = {
                    'ts_event': ts_event,
                    'ts_init': ts_event,
                    'open': bar.open,
                    'high': bar.high,
                    'low': bar.low,
                    'close': bar.close,
                    'volume': bar.volume,
                    'timestamp': ts_event,
                    'date': bar.date
                }
                dict_bars.append(dict_bar)

            except (ValueError, TypeError) as e:
                logger.error(f"Error converting IB bar at {bar.date}: {e}")
                continue

        return dict_bars

    def _generate_progress_report(self, current_date: date, start_date: date,
                                end_date: date, bars_count: int) -> None:
        """
        Generate progress report during download.

        Args:
            current_date: Current date being processed
            start_date: Start date of range
            end_date: End date of range
            bars_count: Number of bars downloaded so far
        """
        total_days = (end_date - start_date).days + 1
        completed_days = (current_date - start_date).days + 1
        progress_pct = (completed_days / total_days) * 100

        logger.info(f"Progress: {completed_days}/{total_days} days ({progress_pct:.1f}%) - "
                   f"{bars_count:,} bars downloaded")

    def test_connection(self) -> bool:
        """
        Test connection to IB Gateway/TWS.

        Returns:
            True if connection successful, False otherwise
        """
        return self.ib_client.test_connection()

    def get_supported_symbols(self) -> List[str]:
        """
        Get list of supported symbols for this downloader.

        Returns:
            List of supported symbol strings
        """
        # Default implementation - subclasses should override
        return []

    def estimate_download_time(self, start_date: date, end_date: date) -> int:
        """
        Estimate download time in seconds.

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            Estimated time in seconds
        """
        total_days = (end_date - start_date).days + 1
        # Rough estimate: 2 seconds per day including pacing
        return total_days * 2

    def _save_bars_to_catalog(self, 
                              bars_data: List[Dict[str, Any]], 
                              symbol: str,
                              bar_type: str = "1-MINUTE-LAST",
                              append_mode: bool = True) -> SaveResult:
        """
        Save bar data to Nautilus catalog.
        
        Args:
            bars_data: List of bar dictionaries
            symbol: Trading symbol
            bar_type: Bar type specification
            append_mode: Whether to append to existing data
            
        Returns:
            SaveResult with operation details
        """
        if not self.use_nautilus_saver or not self.nautilus_saver:
            # Fallback to basic saving (implementation depends on subclass)
            return self._save_bars_fallback(bars_data, symbol)
        
        try:
            # Create a backup before attempting Nautilus save
            backup_result = self._save_bars_fallback(bars_data, f"{symbol}_backup")
            if backup_result.success:
                logger.info(f"Created backup with {backup_result.bars_saved} bars at {backup_result.file_path}")
            
            # Check for existing data if in append mode
            if append_mode:
                existing_info = self.nautilus_saver.check_existing_data(symbol)
                if existing_info.get("exists", False):
                    logger.info(f"Found existing data for {symbol}: "
                              f"{existing_info.get('bar_count', 0)} bars")
                    logger.info(f"Date range: {existing_info.get('date_range', {})}")
            
            # Save to Nautilus catalog
            save_result = self.nautilus_saver.save_bars(
                bars_data=bars_data,
                symbol=symbol,
                bar_type=bar_type,
                append_mode=append_mode
            )
            
            if save_result.success:
                logger.info(f"Successfully saved {save_result.bars_saved} bars to Nautilus catalog")
                if save_result.append_mode_used:
                    logger.info("Data was appended to existing catalog data")
            else:
                logger.error(f"Failed to save to Nautilus catalog: {save_result.error_message}")
            
            return save_result
            
        except Exception as e:
            logger.error(f"Error saving to Nautilus catalog: {e}")
            return SaveResult(
                success=False,
                bars_saved=0,
                file_path="",
                error_message=str(e)
            )
    
    def _save_bars_fallback(self, bars_data: List[Dict[str, Any]], symbol: str) -> SaveResult:
        """
        Fallback method for saving bars when Nautilus catalog is not available.
        
        Args:
            bars_data: List of bar dictionaries
            symbol: Trading symbol
            
        Returns:
            SaveResult with operation details
        """
        # Basic implementation - subclasses can override for specific formats
        logger.warning("Using fallback data saving (Nautilus catalog not available)")
        
        try:
            import pandas as pd
            
            # Convert to DataFrame
            df = pd.DataFrame(bars_data)
            
            # Create output directory
            output_dir = Path("data") / symbol
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = output_dir / f"{symbol}_bars_{timestamp}.parquet"
            
            # Save as parquet
            df.to_parquet(output_file, engine='pyarrow')
            
            logger.info(f"Saved {len(bars_data)} bars to fallback file: {output_file}")
            
            return SaveResult(
                success=True,
                bars_saved=len(bars_data),
                file_path=str(output_file)
            )
            
        except Exception as e:
            logger.error(f"Error in fallback data saving: {e}")
            return SaveResult(
                success=False,
                bars_saved=0,
                file_path="",
                error_message=str(e)
            )
    
    def get_catalog_info(self, symbol: str) -> Dict[str, Any]:
        """
        Get information about existing data in the catalog.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with catalog information
        """
        if not self.use_nautilus_saver or not self.nautilus_saver:
            return {"error": "Nautilus catalog not available"}
        
        try:
            return self.nautilus_saver.check_existing_data(symbol)
        except Exception as e:
            logger.error(f"Error getting catalog info: {e}")
            return {"error": str(e)}
    
    def validate_catalog(self) -> Dict[str, Any]:
        """
        Validate the Nautilus catalog.
        
        Returns:
            Validation results
        """
        if not self.use_nautilus_saver or not self.nautilus_saver:
            return {
                "is_valid": False,
                "error": "Nautilus catalog not available"
            }
        
        try:
            return self.nautilus_saver.validate_catalog()
        except Exception as e:
            logger.error(f"Error validating catalog: {e}")
            return {
                "is_valid": False,
                "error": str(e)
            }
