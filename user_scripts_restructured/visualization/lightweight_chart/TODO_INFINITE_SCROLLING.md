# Infinite Scrolling Implementation TODO
**Date:** June 9, 2025
**Status:** 🚀 Ready to Start (Plan Revised)

---

## 🎯 Project Overview (from Plan)

### Current Limitations
- Hard-coded 5000-point data limit
- Bulk loading architecture (all data upfront)
- Poor performance with large datasets
- No progressive data loading

### Target Architecture
- **Initial Load**: Configurable recent bars (e.g., 1000-2000) for fast display.
- **Progressive Loading**: Configurable bar chunks (e.g., 200-500) as user scrolls back.
- **Unlimited History**: Access years of data without significant performance penalty.
- **Memory Management**: Intelligent data trimming and caching.

---

## 📋 Phase 1: Core Infinite Scrolling Implementation ⚡ *Priority: HIGH*
**Timeline Estimate (Adjust as needed):** 3-5 days
**Goal:** Basic infinite scrolling functionality

### Backend API Development
- [ ] **1.1** Create new API endpoint `/api/chart-data-chunk/<instrument_id>` in `app.py`.
  - [ ] Parameters: `timeframe`, `before_timestamp_seconds`, `chunk_size`.
  - [ ] Utilize `data_loader.load_instrument_data_optimized()` with `limit` (as `chunk_size`) and `before_datetime`.
  - [ ] Return JSON response with `ohlc`, `volume`, and `metadata`.
  - [ ] Metadata to include: `earliest_timestamp`, `latest_timestamp`, `has_more_data`, `total_bars_loaded`.
  - [ ] Test endpoint thoroughly with Postman/curl.

- [ ] **1.2** Modify existing API `/api/chart-data/<instrument_id>` (if necessary) or ensure it can handle smaller initial load sizes.
  - [ ] Parameter: `limit` for initial load size.
  - [ ] Parameter (optional): `initial_load=true` flag if sharing endpoint logic.
  - [ ] Test initial load functionality.

- [ ] **1.3** Add error handling for chunk loading failures in API responses.

### Frontend Implementation (chart.html & related JS)
- [ ] **1.4** Modify `subscribeVisibleLogicalRangeChange` for data loading detection.
  - [ ] Implement threshold-based loading trigger (e.g., when `logicalRange.from < scroll_threshold` config value).
  - [ ] Add loading state management (e.g., `isLoadingHistoricalData`) to prevent concurrent loads.

- [ ] **1.5** Create `loadHistoricalChunk()` JavaScript function.
  - [ ] Fetch data from `/api/chart-data-chunk/`.
  - [ ] Handle API response and metadata.

- [ ] **1.6** Implement data merging logic.
  - [ ] Create function to merge historical chunks with existing chart data (prepend).
  - [ ] Ensure temporal continuity and correct ordering.
  - [ ] Handle volume data merging, preserving color coding if applicable.
  - [ ] Preserve chart state (zoom, position if possible) during data updates.
  - [ ] Update candlestick and volume series with `setData()`.

- [ ] **1.7** Add basic loading indicators and UX.
  - [ ] Create a subtle loading indicator for background chunk loading.
  - [ ] Add basic error messages for failed chunk loads on the frontend.
  - [ ] Implement simple retry logic (e.g., one retry on failure).

### Configuration Updates (config.py)
- [ ] **1.8** Define and implement `InfiniteScrollConfig` dataclass in `config.py`.
  - [ ] `initial_load_size: int`
  - [ ] `initial_load_by_timeframe: Dict[str, int]`
  - [ ] `chunk_size: int`
  - [ ] `chunk_size_by_timeframe: Dict[str, int]`
  - [ ] `scroll_threshold: int` (e.g., 10 bars from edge to trigger load)
  - [ ] `enable_infinite_scrolling: bool` (feature flag)
- [ ] **1.9** Integrate `InfiniteScrollConfig` into `app.py` and pass relevant values to frontend if needed.
- [ ] **1.10** Test configuration loading and validation.

### Integration & Testing (Phase 1)
- [ ] **1.11** Test complete infinite scroll user flow for various instruments and timeframes.
- [ ] **1.12** Verify performance with different timeframes (initial load, chunk load).
- [ ] **1.13** Test basic error handling and recovery (API errors, empty chunks).
- [ ] **1.14** Validate data accuracy and completeness after merging chunks.
- [ ] **1.15** Initial cross-browser compatibility check (Chrome, Firefox).

### Performance Validation (Phase 1 Baseline)
- [ ] **1.16** Measure initial load time improvements (vs. old 5000 bar load).
- [ ] **1.17** Benchmark chunk loading performance.
- [ ] **1.18** Monitor memory usage during basic scrolling.
- [ ] **1.19** Test with a reasonably large dataset (e.g., a few months of 1min data).

---

## 📋 Phase 2: Memory Management & Optimization 🧠 *Priority: MEDIUM*
**Timeline Estimate (Adjust as needed):** 2-3 days
**Goal:** Intelligent memory management and performance optimization

### Memory Management
- [ ] **2.1** Implement data trimming system in frontend JavaScript.
  - [ ] Add `trimFutureData()` (or similar) function to remove newest data when a memory/point limit is exceeded.
  - [ ] Use `InfiniteScrollConfig.max_memory_points`.
  - [ ] Use `InfiniteScrollConfig.trim_size_when_exceeded`.
- [ ] **2.2** Implement LRU-style eviction policy (conceptual: when loading old data and memory is full, trim from the "future" end of the dataset).
- [ ] **2.3** Add basic memory usage monitoring (e.g., log number of points in memory).
- [ ] **2.4** Test memory management under heavy scrolling.

### Performance Optimization
- [ ] **2.5** Implement intelligent prefetching (optional, based on initial performance).
  - [ ] Detect scroll velocity and direction.
  - [ ] Prefetch next chunk if user is scrolling consistently backward.
  - [ ] Use `InfiniteScrollConfig.enable_prefetching` and `prefetch_chunks`.
- [ ] **2.6** Implement request deduplication for chunk loads (ensure `isLoadingHistoricalData` is robust).
- [ ] **2.7** Optimize data merging JavaScript function for performance with larger datasets.

### Caching Strategy (Frontend/Browser)
- [ ] **2.8** Consider basic browser caching for chunks if beneficial (e.g., using service workers or standard HTTP caching headers from API).
  - [ ] This might be complex; evaluate necessity vs. server-side efficiency.
- [ ] **2.9** Ensure cache invalidation logic if client-side caching is implemented, especially for real-time updates (though real-time is Phase 3).

---

## 📋 Phase 3: Advanced Features & Polish ✨ *Priority: LOW*
**Timeline Estimate (Adjust as needed):** 2-3 days
**Goal:** Enhanced user experience and advanced functionality

### Advanced Loading Features
- [ ] **3.1** Bidirectional infinite scrolling (loading future data/newer data than initial load).
  - [ ] Requires new API endpoint or modification to existing chunk loader for "after_timestamp_seconds".
  - [ ] Update frontend logic to detect scrolling towards the future.
- [ ] **3.2** Handle real-time data integration with infinite scroll.
  - [ ] How new ticks merge/affect loaded historical data.
  - [ ] Cache invalidation for chunks affected by live data.
- [ ] **3.3** Handle data gaps and missing periods gracefully.

### Enhanced User Experience
- [ ] **3.4** Add smooth loading animations or more refined progress indicators.
- [ ] **3.5** Implement scroll position preservation more robustly (e.g., maintain view on a specific bar timestamp).
- [ ] **3.6** Add visual feedback for data loading boundaries or when all historical data is loaded.
- [ ] **3.7** Add keyboard navigation shortcuts for scrolling (if not already present).
- [ ] **3.8** Implement zoom-to-fit logic that considers dynamically loaded data.

### Monitoring & Analytics (Optional - for deeper insights)
- [ ] **3.9** Add frontend performance metrics collection (e.g., chunk load times, merge times).
- [ ] **3.10** Implement basic usage analytics (e.g., how far back users scroll).

---

## 🎯 Implementation Priority Queue (Consolidated)

### 🔥 **Critical Path Items (Phase 1 - Basic Functionality)**
1.  ✅ Project planning and documentation (DONE - this document is part of it)
2.  ⏳ **1.1** Create `/api/chart-data-chunk` endpoint in `app.py`.
3.  ⏳ **1.8** Define and implement `InfiniteScrollConfig` in `config.py`.
4.  ⏳ **1.4** Modify frontend `subscribeVisibleLogicalRangeChange`.
5.  ⏳ **1.5** Create `loadHistoricalChunk()` JS function.
6.  ⏳ **1.6** Implement data merging logic in JS.
7.  ⏳ **1.7** Add basic loading indicators and frontend error handling.
8.  ⏳ **1.11 - 1.19** Core integration testing and performance validation for Phase 1.

### ⚡ **High Priority (Phase 1 Completion & Stability)**
9.  **1.2** Ensure initial load API works with new config.
10. **1.3** Robust API error handling for chunks.
11. **1.9, 1.10** Full configuration integration and testing.

### 🛠️ **Medium Priority (Phase 2 - Optimization & Memory)**
12. **2.1 - 2.4** Memory management system (trimming, limits).
13. **2.5 - 2.7** Performance optimizations (prefetching, deduplication, merge optimization).
14. **2.8 - 2.9** Caching strategy evaluation and implementation if deemed necessary.

### ✨ **Nice to Have (Phase 3 - Advanced Features & Polish)**
15. **3.1 - 3.3** Bidirectional scrolling, real-time integration, gap handling.
16. **3.4 - 3.8** Enhanced UX (animations, scroll preservation,
